from typing import Optional
from uuid import UUID
import logging

from app.core.database import get_supabase_client
from app.models.tarea_cliente import (
    TareaClienteCreate,
    TareaClienteUpdate,
    TareaClienteListItem,
    TareaClienteDetalle,
    TareaClienteListResponse,
    TareaClienteFilters
)
from app.models.proceso_cliente import PersonaResponsable
from app.services.proceso_cliente_service import calcular_tiempo_normalizado_horas_mes

logger = logging.getLogger(__name__)


async def get_tareas_cliente_by_proceso(
    proceso_id: UUID,
    filters: Optional[TareaClienteFilters] = None,
    skip: int = 0,
    limit: int = 100
) -> TareaClienteListResponse:
    """
    Get list of client tasks for a specific process with filtering.
    """
    supabase = await get_supabase_client()
    
    try:
        # Base query
        query = supabase.table('tareas_clientes').select(
            """
            id,
            proceso_cliente_id,
            nombre_tarea_cliente,
            descripcion_tarea_cliente,
            duracion_minutos_por_ejecucion,
            frecuencia_periodo,
            frecuencia_ocurrencias,
            es_manual_cliente,
            herramientas_utilizadas_cliente,
            puntos_dolor_cliente,
            oportunidades_mejora_cliente,
            info_adicional,
            created_at,
            updated_at
            """
        ).eq('proceso_cliente_id', str(proceso_id))
        
        # Apply filters
        if filters:
            if filters.es_manual_cliente is not None:
                query = query.eq('es_manual_cliente', filters.es_manual_cliente)
            
            if filters.search:
                # Use ilike for case-insensitive search
                search_term = f"%{filters.search}%"
                query = query.or_(f"nombre_tarea_cliente.ilike.{search_term},descripcion_tarea_cliente.ilike.{search_term}")
        
        # Execute query
        response = query.range(skip, skip + limit - 1).execute()
        
        if not response.data:
            return TareaClienteListResponse(
                tareas=[],
                total=0,
                tiempo_total_horas_mes=0.0
            )
        
        # Get all task IDs for batch queries
        tarea_ids = [tarea['id'] for tarea in response.data]

        # Batch query for all task responsibles
        responsables_response = supabase.table('tareas_clientes_responsables').select(
            "tarea_cliente_id, persona_cliente_id"
        ).in_('tarea_cliente_id', tarea_ids).execute()

        # Group responsables by task
        responsables_por_tarea = {}
        all_persona_ids = set()
        for resp in (responsables_response.data or []):
            tarea_id = resp['tarea_cliente_id']
            persona_id = resp['persona_cliente_id']
            if tarea_id not in responsables_por_tarea:
                responsables_por_tarea[tarea_id] = []
            responsables_por_tarea[tarea_id].append(persona_id)
            all_persona_ids.add(persona_id)

        # Batch query for all personas
        personas_dict = {}
        departamento_ids = set()
        if all_persona_ids:
            personas_response = supabase.table('personas').select(
                "id, nombre, apellidos, cargo, departamento_id"
            ).in_('id', list(all_persona_ids)).execute()

            for persona in (personas_response.data or []):
                personas_dict[persona['id']] = persona
                if persona.get('departamento_id'):
                    departamento_ids.add(persona['departamento_id'])

        # Batch query for all departments
        departamentos_dict = {}
        if departamento_ids:
            dept_response = supabase.table('departamentos').select(
                "id, nombre"
            ).in_('id', list(departamento_ids)).execute()

            for dept in (dept_response.data or []):
                departamentos_dict[dept['id']] = dept

        # Process results and enrich with related data
        tareas_list = []
        total_tiempo_horas_mes = 0.0

        for tarea_data in response.data:
            # Calculate normalized time
            tiempo_estimado = calcular_tiempo_normalizado_horas_mes(
                tarea_data.get('duracion_minutos_por_ejecucion'),
                tarea_data.get('frecuencia_periodo'),
                tarea_data.get('frecuencia_ocurrencias')
            )

            # Get responsible persons from cached data
            responsables = []
            persona_ids = responsables_por_tarea.get(tarea_data['id'], [])

            for persona_id in persona_ids:
                persona = personas_dict.get(persona_id)
                if persona:
                    # Get department name from cached data
                    dept_name = None
                    if persona.get('departamento_id'):
                        dept = departamentos_dict.get(persona['departamento_id'])
                        if dept:
                            dept_name = dept['nombre']

                    responsables.append(PersonaResponsable(
                        id=persona['id'],
                        nombre=persona['nombre'],
                        apellidos=persona.get('apellidos'),
                        cargo=persona.get('cargo'),
                        departamento_nombre=dept_name
                    ))

            # Create task list item
            tarea_item = TareaClienteListItem(
                **tarea_data,
                tiempo_estimado_horas_mes=tiempo_estimado,
                responsables=responsables
            )

            tareas_list.append(tarea_item)
            total_tiempo_horas_mes += tiempo_estimado
        
        # Apply person filter if specified (after enriching data)
        if filters and filters.responsable_ids:
            filtered_tareas = []
            for tarea in tareas_list:
                # Check if any of the specified persons is responsible for this task
                tarea_person_ids = {resp.id for resp in tarea.responsables}
                
                # Check if any filter person is responsible for this task
                if any(person_id in tarea_person_ids for person_id in filters.responsable_ids):
                    filtered_tareas.append(tarea)
            
            tareas_list = filtered_tareas
            # Recalculate total time
            total_tiempo_horas_mes = sum(t.tiempo_estimado_horas_mes or 0 for t in tareas_list)
        
        return TareaClienteListResponse(
            tareas=tareas_list,
            total=len(tareas_list),
            tiempo_total_horas_mes=round(total_tiempo_horas_mes, 2)
        )
        
    except Exception as e:
        logger.error(f"Error getting client tasks: {e}")
        raise Exception(f"Error retrieving client tasks: {str(e)}")


async def get_tarea_cliente_detalle(tarea_id: UUID) -> Optional[TareaClienteDetalle]:
    """
    Get detailed information for a specific client task.
    """
    supabase = await get_supabase_client()
    
    try:
        # Get task data
        response = supabase.table('tareas_clientes').select("*").eq('id', str(tarea_id)).execute()
        
        if not response.data:
            return None
        
        tarea_data = response.data[0]
        
        # Calculate normalized time
        tiempo_estimado = calcular_tiempo_normalizado_horas_mes(
            tarea_data.get('duracion_minutos_por_ejecucion'),
            tarea_data.get('frecuencia_periodo'),
            tarea_data.get('frecuencia_ocurrencias')
        )
        
        # Get responsible persons
        responsables = []
        resp_response = supabase.table('tareas_clientes_responsables').select(
            "persona_cliente_id"
        ).eq('tarea_cliente_id', str(tarea_id)).execute()

        if resp_response.data:
            persona_ids = [r['persona_cliente_id'] for r in resp_response.data]
            if persona_ids:
                # Batch query for personas
                personas_response = supabase.table('personas').select(
                    "id, nombre, apellidos, cargo, departamento_id"
                ).in_('id', persona_ids).execute()

                # Get all department IDs for batch query
                dept_ids = set()
                personas_dict = {}
                for persona in (personas_response.data or []):
                    personas_dict[persona['id']] = persona
                    if persona.get('departamento_id'):
                        dept_ids.add(persona['departamento_id'])

                # Batch query for departments
                departamentos_dict = {}
                if dept_ids:
                    dept_response = supabase.table('departamentos').select(
                        "id, nombre"
                    ).in_('id', list(dept_ids)).execute()

                    for dept in (dept_response.data or []):
                        departamentos_dict[dept['id']] = dept

                # Build responsables list
                for persona_id in persona_ids:
                    persona = personas_dict.get(persona_id)
                    if persona:
                        # Get department name from cached data
                        dept_name = None
                        if persona.get('departamento_id'):
                            dept = departamentos_dict.get(persona['departamento_id'])
                            if dept:
                                dept_name = dept['nombre']

                        responsables.append(PersonaResponsable(
                            id=persona['id'],
                            nombre=persona['nombre'],
                            apellidos=persona.get('apellidos'),
                            cargo=persona.get('cargo'),
                            departamento_nombre=dept_name
                        ))
        
        tarea_detalle = TareaClienteDetalle(
            **tarea_data,
            tiempo_estimado_horas_mes=tiempo_estimado,
            responsables=responsables
        )
        
        return tarea_detalle
        
    except Exception as e:
        logger.error(f"Error getting task detail: {e}")
        raise Exception(f"Error retrieving task detail: {str(e)}")


async def update_tarea_cliente(tarea_id: UUID, update_data: TareaClienteUpdate) -> Optional[TareaClienteDetalle]:
    """
    Update a client task.
    """
    supabase = await get_supabase_client()
    
    try:
        # Prepare update data
        update_dict = update_data.model_dump(exclude_unset=True)
        
        if not update_dict:
            # No changes to apply
            return await get_tarea_cliente_detalle(tarea_id)
        
        # Convert enum values to strings
        for key, value in update_dict.items():
            if hasattr(value, 'value'):
                update_dict[key] = value.value
        
        # Update the task
        response = supabase.table('tareas_clientes').update(update_dict).eq('id', str(tarea_id)).execute()
        
        if not response.data:
            return None
        
        # Return updated task detail
        return await get_tarea_cliente_detalle(tarea_id)
        
    except Exception as e:
        logger.error(f"Error updating task: {e}")
        raise Exception(f"Error updating task: {str(e)}")


async def create_tarea_cliente(tarea_data: TareaClienteCreate) -> TareaClienteDetalle:
    """
    Create a new client task.
    """
    supabase = await get_supabase_client()
    
    try:
        # Prepare create data
        create_dict = tarea_data.model_dump()
        
        # Convert enum values to strings
        for key, value in create_dict.items():
            if hasattr(value, 'value'):
                create_dict[key] = value.value
        
        # Create the task
        response = supabase.table('tareas_clientes').insert(create_dict).execute()
        
        if not response.data:
            raise Exception("Failed to create task")
        
        created_task = response.data[0]
        
        # Return created task detail
        return await get_tarea_cliente_detalle(UUID(created_task['id']))
        
    except Exception as e:
        logger.error(f"Error creating task: {e}")
        raise Exception(f"Error creating task: {str(e)}")


async def delete_tarea_cliente(tarea_id: UUID) -> bool:
    """
    Delete a client task.
    """
    supabase = await get_supabase_client()
    
    try:
        # First delete related responsible persons
        supabase.table('tareas_clientes_responsables').delete().eq('tarea_cliente_id', str(tarea_id)).execute()
        
        # Delete the task
        response = supabase.table('tareas_clientes').delete().eq('id', str(tarea_id)).execute()
        
        return len(response.data or []) > 0
        
    except Exception as e:
        logger.error(f"Error deleting task: {e}")
        raise Exception(f"Error deleting task: {str(e)}")
