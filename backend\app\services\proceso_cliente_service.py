from typing import Optional
from uuid import UUID
import logging

from app.core.database import get_supabase_client
from app.core.cache import (
    get_cache,
    cache_key_contadores_empresa,
)
from app.models.proceso_cliente import (
    ProcesoClienteCreate,
    ProcesoClienteUpdate,
    ProcesoClienteListItem,
    ProcesoClienteDetalle,
    ProcesoClienteListResponse,
    ProcesoClienteFilters,
    ProcesoClienteGroupBy,
    PersonaResponsable,
    DepartamentoInfo,
    FACTOR_CONVERSION_MENSUAL,
    UnidadRepeticion
)
from app.models.tarea_cliente import (
    TareaClienteDetalle,
    TareaClienteFilters,
    ProcesoClienteContadores
)

logger = logging.getLogger(__name__)


def calcular_tiempo_normalizado_horas_mes(
    duracion_minutos: Optional[int],
    unidad_repeticion: Optional[str],
    numero_ocurrencias: Optional[int]
) -> float:
    """
    Calculate normalized time in hours per month.
    
    Formula: (duracion_minutos * numero_ocurrencias / 60) * factor_conversion_mensual
    """
    if not duracion_minutos or not unidad_repeticion or not numero_ocurrencias:
        return 0.0
    
    try:
        # Convert string to enum if needed
        if isinstance(unidad_repeticion, str):
            unidad_enum = UnidadRepeticion(unidad_repeticion)
        else:
            unidad_enum = unidad_repeticion
        
        factor = FACTOR_CONVERSION_MENSUAL.get(unidad_enum, 0)
        tiempo_horas_mes = (duracion_minutos * numero_ocurrencias / 60) * factor
        return round(tiempo_horas_mes, 2)
    except (ValueError, KeyError) as e:
        logger.warning(f"Error calculating normalized time: {e}")
        return 0.0


async def get_procesos_clientes_con_metricas(
    empresa_id: UUID,
    filters: Optional[ProcesoClienteFilters] = None,
    group_by: Optional[ProcesoClienteGroupBy] = None,
    skip: int = 0,
    limit: int = 100
) -> ProcesoClienteListResponse:
    """
    Get list of client processes with metrics and filtering.
    """
    supabase = await get_supabase_client()
    
    try:
        # Base query
        query = supabase.table('procesos_clientes').select(
            """
            id,
            nombre,
            descripcion,
            departamento_cliente_id,
            estado_analisis,
            es_repetitivo,
            es_cuello_botella,
            es_manual,
            valor_negocio_cliente,
            complejidad_automatizacion_aceleralia,
            prioridad_automatizacion_aceleralia,
            duracion_minutos_por_ejecucion,
            frecuencia_periodo,
            frecuencia_ocurrencias,
            herramientas_utilizadas_cliente,
            reunion_origen_id,
            info_adicional,
            created_at,
            updated_at,
            empresa_cliente_id
            """
        ).eq('empresa_cliente_id', str(empresa_id))
        
        # Apply filters
        if filters:
            if filters.departamento_id:
                query = query.eq('departamento_cliente_id', str(filters.departamento_id))
            
            if filters.es_cuello_botella is not None:
                query = query.eq('es_cuello_botella', filters.es_cuello_botella)
            
            if filters.valor_negocio_cliente:
                query = query.eq('valor_negocio_cliente', filters.valor_negocio_cliente.value)
            
            if filters.es_manual is not None:
                query = query.eq('es_manual', filters.es_manual)
            
            if filters.es_repetitivo is not None:
                query = query.eq('es_repetitivo', filters.es_repetitivo)
            
            if filters.complejidad_automatizacion_aceleralia:
                query = query.eq('complejidad_automatizacion_aceleralia', filters.complejidad_automatizacion_aceleralia.value)
            
            if filters.prioridad_automatizacion_aceleralia:
                query = query.eq('prioridad_automatizacion_aceleralia', filters.prioridad_automatizacion_aceleralia.value)
            
            if filters.search:
                # Use ilike for case-insensitive search
                search_term = f"%{filters.search}%"
                query = query.or_(f"nombre.ilike.{search_term},descripcion.ilike.{search_term}")
        
        # Execute query
        response = query.range(skip, skip + limit - 1).execute()
        
        if not response.data:
            return ProcesoClienteListResponse(
                procesos=[],
                total=0,
                tiempo_total_horas_mes=0.0,
                total_tareas=0
            )
        
        # Get total count
        count_response = supabase.table('procesos_clientes').select("id", count="exact").eq('empresa_cliente_id', str(empresa_id)).execute()
        total_count = count_response.count or 0
        
        # Get all process IDs for batch queries
        proceso_ids = [p['id'] for p in response.data]

        # Batch query for all tasks
        all_tareas_response = supabase.table('tareas_clientes').select(
            "id, proceso_cliente_id, duracion_minutos_por_ejecucion, frecuencia_periodo, frecuencia_ocurrencias"
        ).in_('proceso_cliente_id', proceso_ids).execute()

        # Group tasks by process
        tareas_por_proceso = {}
        for tarea in (all_tareas_response.data or []):
            proceso_id = tarea['proceso_cliente_id']
            if proceso_id not in tareas_por_proceso:
                tareas_por_proceso[proceso_id] = []
            tareas_por_proceso[proceso_id].append(tarea)

        # Get all responsible persons from intermediate table
        all_personas_ids = set()

        # Get all responsible persons ordered by created_at to determine principal
        responsables_response = supabase.table('procesos_clientes_responsables').select(
            "proceso_cliente_id, persona_cliente_id, created_at"
        ).in_('proceso_cliente_id', proceso_ids).order('created_at').execute()

        responsables_por_proceso = {}
        for resp in (responsables_response.data or []):
            proceso_id = resp['proceso_cliente_id']
            if proceso_id not in responsables_por_proceso:
                responsables_por_proceso[proceso_id] = []
            responsables_por_proceso[proceso_id].append({
                'persona_id': resp['persona_cliente_id'],
                'created_at': resp['created_at']
            })
            all_personas_ids.add(resp['persona_cliente_id'])

        # Batch query for all persons
        personas_dict = {}
        if all_personas_ids:
            personas_response = supabase.table('personas').select(
                "id, nombre, apellidos, cargo, departamento_id"
            ).in_('id', list(all_personas_ids)).execute()

            for persona in (personas_response.data or []):
                personas_dict[persona['id']] = persona

        # Batch query for all departments
        all_dept_ids = set()
        for proceso_data in response.data:
            if proceso_data.get('departamento_cliente_id'):
                all_dept_ids.add(proceso_data['departamento_cliente_id'])

        for persona in personas_dict.values():
            if persona.get('departamento_id'):
                all_dept_ids.add(persona['departamento_id'])

        departamentos_dict = {}
        if all_dept_ids:
            dept_response = supabase.table('departamentos').select(
                "id, nombre, descripcion"
            ).in_('id', list(all_dept_ids)).execute()

            for dept in (dept_response.data or []):
                departamentos_dict[dept['id']] = dept

        # Process results and enrich with related data
        procesos_list = []
        total_tiempo_horas_mes = 0.0
        total_tareas_count = 0

        for proceso_data in response.data:
            # Calculate normalized time
            tiempo_estimado = calcular_tiempo_normalizado_horas_mes(
                proceso_data.get('duracion_minutos_por_ejecucion'),
                proceso_data.get('frecuencia_periodo'),
                proceso_data.get('frecuencia_ocurrencias')
            )

            # Get tasks for this process
            tareas = tareas_por_proceso.get(proceso_data['id'], [])
            numero_tareas = len(tareas)
            tiempo_tareas = 0.0

            for tarea in tareas:
                tiempo_tarea = calcular_tiempo_normalizado_horas_mes(
                    tarea.get('duracion_minutos_por_ejecucion'),
                    tarea.get('frecuencia_periodo'),
                    tarea.get('frecuencia_ocurrencias')
                )
                tiempo_tareas += tiempo_tarea

            # Use task time if available, otherwise use process time
            tiempo_final = tiempo_tareas if tiempo_tareas > 0 else tiempo_estimado
            
            # Get responsible persons from cached data
            responsable_principal = None
            otros_responsables = []

            # Get all responsables for this process
            responsables_proceso = responsables_por_proceso.get(proceso_data['id'], [])

            # Sort by created_at to determine principal (first one)
            responsables_proceso.sort(key=lambda x: x['created_at'])

            for i, resp_data in enumerate(responsables_proceso):
                persona = personas_dict.get(resp_data['persona_id'])
                if persona:
                    # Get department name from cached data
                    dept_name = None
                    if persona.get('departamento_id'):
                        dept = departamentos_dict.get(persona['departamento_id'])
                        if dept:
                            dept_name = dept['nombre']

                    persona_responsable = PersonaResponsable(
                        id=persona['id'],
                        nombre=persona['nombre'],
                        apellidos=persona.get('apellidos'),
                        cargo=persona.get('cargo'),
                        departamento_nombre=dept_name
                    )

                    # First one is principal, rest are others
                    if i == 0:
                        responsable_principal = persona_responsable
                    else:
                        otros_responsables.append(persona_responsable)

            # Get department info from cached data
            departamento = None
            if proceso_data.get('departamento_cliente_id'):
                dept = departamentos_dict.get(proceso_data['departamento_cliente_id'])
                if dept:
                    departamento = DepartamentoInfo(
                        id=dept['id'],
                        nombre=dept['nombre'],
                        descripcion=dept.get('descripcion')
                    )

            # Create process list item
            proceso_item = ProcesoClienteListItem(
                **proceso_data,
                tiempo_estimado_horas_mes=tiempo_final,
                numero_tareas=numero_tareas,
                responsable_principal=responsable_principal,
                departamento=departamento,
                otros_responsables=otros_responsables
            )
            
            procesos_list.append(proceso_item)
            total_tiempo_horas_mes += tiempo_final
            total_tareas_count += numero_tareas
        
        # Apply person filter if specified (after enriching data)
        if filters and filters.responsable_ids:
            # Get task responsible persons for all processes in one query
            all_tarea_ids = []
            for proceso_id in proceso_ids:
                tareas = tareas_por_proceso.get(proceso_id, [])
                all_tarea_ids.extend([t['id'] for t in tareas])

            tareas_responsables_dict = {}
            if all_tarea_ids:
                tareas_resp_response = supabase.table('tareas_clientes_responsables').select(
                    "tarea_cliente_id, persona_cliente_id"
                ).in_('tarea_cliente_id', all_tarea_ids).execute()

                for resp in (tareas_resp_response.data or []):
                    tarea_id = resp['tarea_cliente_id']
                    if tarea_id not in tareas_responsables_dict:
                        tareas_responsables_dict[tarea_id] = []
                    tareas_responsables_dict[tarea_id].append(UUID(resp['persona_cliente_id']))

            filtered_procesos = []
            for proceso in procesos_list:
                # Check if any of the specified persons is related to this process
                proceso_person_ids = set()

                # Add principal responsible
                if proceso.responsable_principal:
                    proceso_person_ids.add(proceso.responsable_principal.id)

                # Add other responsible
                for resp in proceso.otros_responsables:
                    proceso_person_ids.add(resp.id)

                # Add task responsible persons from cached data
                tareas = tareas_por_proceso.get(str(proceso.id), [])
                for tarea in tareas:
                    tarea_responsables = tareas_responsables_dict.get(tarea['id'], [])
                    proceso_person_ids.update(tarea_responsables)

                # Check if any filter person is in this process
                if any(person_id in proceso_person_ids for person_id in filters.responsable_ids):
                    filtered_procesos.append(proceso)

            procesos_list = filtered_procesos
            # Recalculate totals
            total_tiempo_horas_mes = sum(p.tiempo_estimado_horas_mes or 0 for p in procesos_list)
            total_tareas_count = sum(p.numero_tareas or 0 for p in procesos_list)
        
        return ProcesoClienteListResponse(
            procesos=procesos_list,
            total=len(procesos_list),
            tiempo_total_horas_mes=round(total_tiempo_horas_mes, 2),
            total_tareas=total_tareas_count
        )
        
    except Exception as e:
        logger.error(f"Error getting client processes: {e}")
        raise Exception(f"Error retrieving client processes: {str(e)}")


async def get_proceso_cliente_detalle(proceso_id: UUID) -> Optional[ProcesoClienteDetalle]:
    """
    Get detailed information for a specific client process.
    """
    supabase = await get_supabase_client()
    
    try:
        # Get process data
        response = supabase.table('procesos_clientes').select("*").eq('id', str(proceso_id)).execute()
        
        if not response.data:
            return None
        
        proceso_data = response.data[0]
        
        # Calculate normalized time
        tiempo_estimado = calcular_tiempo_normalizado_horas_mes(
            proceso_data.get('duracion_minutos_por_ejecucion'),
            proceso_data.get('frecuencia_periodo'),
            proceso_data.get('frecuencia_ocurrencias')
        )
        
        # Get tasks for this process
        tareas_response = supabase.table('tareas_clientes').select("*").eq('proceso_cliente_id', str(proceso_id)).execute()
        
        tareas_list = []
        tiempo_tareas_total = 0.0
        
        # Get all task IDs for batch queries
        tarea_ids = [t['id'] for t in (tareas_response.data or [])]

        # Batch query for all task responsables
        tareas_responsables_dict = {}
        all_persona_ids = set()

        if tarea_ids:
            resp_response = supabase.table('tareas_clientes_responsables').select(
                "tarea_cliente_id, persona_cliente_id"
            ).in_('tarea_cliente_id', tarea_ids).execute()

            for resp in (resp_response.data or []):
                tarea_id = resp['tarea_cliente_id']
                persona_id = resp['persona_cliente_id']
                if tarea_id not in tareas_responsables_dict:
                    tareas_responsables_dict[tarea_id] = []
                tareas_responsables_dict[tarea_id].append(persona_id)
                all_persona_ids.add(persona_id)

        # Batch query for all personas with departments
        personas_dict = {}
        departamentos_dict = {}

        if all_persona_ids:
            personas_response = supabase.table('personas').select(
                "id, nombre, apellidos, cargo, departamento_id"
            ).in_('id', list(all_persona_ids)).execute()

            dept_ids = set()
            for persona in (personas_response.data or []):
                personas_dict[persona['id']] = persona
                if persona.get('departamento_id'):
                    dept_ids.add(persona['departamento_id'])

            # Batch query for departments
            if dept_ids:
                dept_response = supabase.table('departamentos').select(
                    "id, nombre"
                ).in_('id', list(dept_ids)).execute()

                for dept in (dept_response.data or []):
                    departamentos_dict[dept['id']] = dept

        # Process tasks with cached data
        for tarea_data in (tareas_response.data or []):
            # Calculate task time
            tiempo_tarea = calcular_tiempo_normalizado_horas_mes(
                tarea_data.get('duracion_minutos_por_ejecucion'),
                tarea_data.get('frecuencia_periodo'),
                tarea_data.get('frecuencia_ocurrencias')
            )
            tiempo_tareas_total += tiempo_tarea

            # Get task responsible persons from cached data
            responsables_tarea = []
            persona_ids = tareas_responsables_dict.get(tarea_data['id'], [])

            for persona_id in persona_ids:
                persona = personas_dict.get(persona_id)
                if persona:
                    # Get department name from cached data
                    dept_name = None
                    if persona.get('departamento_id'):
                        dept = departamentos_dict.get(persona['departamento_id'])
                        if dept:
                            dept_name = dept['nombre']

                    responsables_tarea.append(PersonaResponsable(
                        id=persona['id'],
                        nombre=persona['nombre'],
                        apellidos=persona.get('apellidos'),
                        cargo=persona.get('cargo'),
                        departamento_nombre=dept_name
                    ))

            tarea_detalle = TareaClienteDetalle(
                **tarea_data,
                tiempo_estimado_horas_mes=tiempo_tarea,
                responsables=responsables_tarea
            )
            tareas_list.append(tarea_detalle)
        
        # Use task time if available, otherwise use process time
        tiempo_final = tiempo_tareas_total if tiempo_tareas_total > 0 else tiempo_estimado

        # Get process responsible persons from intermediate table
        responsables_response = supabase.table('procesos_clientes_responsables').select(
            "persona_cliente_id, created_at"
        ).eq('proceso_cliente_id', str(proceso_id)).order('created_at').execute()

        responsable_principal = None
        otros_responsables = []

        # Get all persona IDs for batch query
        proceso_persona_ids = [r['persona_cliente_id'] for r in (responsables_response.data or [])]

        # Batch query for personas and departments
        if proceso_persona_ids:
            # Add to existing persona IDs
            all_persona_ids.update(proceso_persona_ids)

            # Re-query personas if we have new IDs
            if not all_persona_ids.issubset(personas_dict.keys()):
                personas_response = supabase.table('personas').select(
                    "id, nombre, apellidos, cargo, departamento_id"
                ).in_('id', list(all_persona_ids)).execute()

                dept_ids = set()
                for persona in (personas_response.data or []):
                    personas_dict[persona['id']] = persona
                    if persona.get('departamento_id'):
                        dept_ids.add(persona['departamento_id'])

                # Re-query departments if needed
                if dept_ids and not dept_ids.issubset(departamentos_dict.keys()):
                    dept_response = supabase.table('departamentos').select(
                        "id, nombre"
                    ).in_('id', list(dept_ids)).execute()

                    for dept in (dept_response.data or []):
                        departamentos_dict[dept['id']] = dept

        # Process responsible persons (first is principal, rest are others)
        for i, resp_data in enumerate(responsables_response.data or []):
            persona = personas_dict.get(resp_data['persona_cliente_id'])
            if persona:
                # Get department name from cached data
                dept_name = None
                if persona.get('departamento_id'):
                    dept = departamentos_dict.get(persona['departamento_id'])
                    if dept:
                        dept_name = dept['nombre']

                persona_responsable = PersonaResponsable(
                    id=persona['id'],
                    nombre=persona['nombre'],
                    apellidos=persona.get('apellidos'),
                    cargo=persona.get('cargo'),
                    departamento_nombre=dept_name
                )

                # First one is principal, rest are others
                if i == 0:
                    responsable_principal = persona_responsable
                else:
                    otros_responsables.append(persona_responsable)

        # Get department info
        departamento = None
        if proceso_data.get('departamento_cliente_id'):
            dept = departamentos_dict.get(proceso_data['departamento_cliente_id'])
            if dept:
                departamento = DepartamentoInfo(
                    id=dept['id'],
                    nombre=dept['nombre'],
                    descripcion=dept.get('descripcion')
                )

        proceso_detalle = ProcesoClienteDetalle(
            **proceso_data,
            tiempo_estimado_horas_mes=tiempo_final,
            numero_tareas=len(tareas_list),
            tareas=tareas_list,
            responsable_principal=responsable_principal,
            departamento=departamento,
            otros_responsables=otros_responsables
        )
        
        return proceso_detalle
        
    except Exception as e:
        logger.error(f"Error getting process detail: {e}")
        raise Exception(f"Error retrieving process detail: {str(e)}")


async def update_proceso_cliente(proceso_id: UUID, update_data: ProcesoClienteUpdate) -> Optional[ProcesoClienteDetalle]:
    """
    Update a client process.
    """
    supabase = await get_supabase_client()
    
    try:
        # Prepare update data
        update_dict = update_data.model_dump(exclude_unset=True)
        
        if not update_dict:
            # No changes to apply
            return await get_proceso_cliente_detalle(proceso_id)
        
        # Convert enum values to strings
        for key, value in update_dict.items():
            if hasattr(value, 'value'):
                update_dict[key] = value.value
        
        # Update the process
        response = supabase.table('procesos_clientes').update(update_dict).eq('id', str(proceso_id)).execute()
        
        if not response.data:
            return None
        
        # Return updated process detail
        return await get_proceso_cliente_detalle(proceso_id)
        
    except Exception as e:
        logger.error(f"Error updating process: {e}")
        raise Exception(f"Error updating process: {str(e)}")


async def get_contadores_procesos_cliente(empresa_id: UUID) -> ProcesoClienteContadores:
    """
    Get summary counters for client processes with optimized queries and caching.
    """
    # Check cache first
    cache = get_cache()
    cache_key = cache_key_contadores_empresa(empresa_id)
    cached_result = cache.get(cache_key)

    if cached_result is not None:
        logger.debug(f"Cache hit for contadores empresa {empresa_id}")
        return cached_result

    supabase = await get_supabase_client()

    try:
        # Count processes directly
        procesos_response = supabase.table('procesos_clientes').select(
            "id, duracion_minutos_por_ejecucion, frecuencia_periodo, frecuencia_ocurrencias"
        ).eq('empresa_cliente_id', str(empresa_id)).execute()

        total_procesos = len(procesos_response.data or [])

        if total_procesos == 0:
            return ProcesoClienteContadores(
                total_procesos=0,
                total_tareas=0,
                tiempo_total_horas_mes=0.0
            )

        # Get process IDs for task counting
        proceso_ids = [p['id'] for p in procesos_response.data]

        # Count tasks directly
        tareas_response = supabase.table('tareas_clientes').select(
            "id, duracion_minutos_por_ejecucion, frecuencia_periodo, frecuencia_ocurrencias"
        ).in_('proceso_cliente_id', proceso_ids).execute()

        total_tareas = len(tareas_response.data or [])

        # Calculate total time from processes
        tiempo_total_procesos = 0.0
        for proceso in procesos_response.data:
            tiempo_proceso = calcular_tiempo_normalizado_horas_mes(
                proceso.get('duracion_minutos_por_ejecucion'),
                proceso.get('frecuencia_periodo'),
                proceso.get('frecuencia_ocurrencias')
            )
            tiempo_total_procesos += tiempo_proceso

        # Calculate total time from tasks
        tiempo_total_tareas = 0.0
        for tarea in (tareas_response.data or []):
            tiempo_tarea = calcular_tiempo_normalizado_horas_mes(
                tarea.get('duracion_minutos_por_ejecucion'),
                tarea.get('frecuencia_periodo'),
                tarea.get('frecuencia_ocurrencias')
            )
            tiempo_total_tareas += tiempo_tarea

        # Use task time if available, otherwise use process time
        tiempo_total_final = tiempo_total_tareas if tiempo_total_tareas > 0 else tiempo_total_procesos

        result = ProcesoClienteContadores(
            total_procesos=total_procesos,
            total_tareas=total_tareas,
            tiempo_total_horas_mes=round(tiempo_total_final, 2)
        )

        # Cache the result for 5 minutes
        cache.set(cache_key, result, ttl=300)
        logger.debug(f"Cached contadores for empresa {empresa_id}")

        return result

    except Exception as e:
        logger.error(f"Error getting process counters: {e}")
        raise Exception(f"Error retrieving process counters: {str(e)}")
