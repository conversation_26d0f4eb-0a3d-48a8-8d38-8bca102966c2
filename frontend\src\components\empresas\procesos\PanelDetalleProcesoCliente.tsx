import React, { useState } from 'react';
import LoadingSpinner from '../../UI/LoadingSpinner';
import { EditableField } from '../../UI/EditableField';
import styles from './PanelDetalleProcesoCliente.module.css';
import type {
  ProcesoClienteDetalle,
  ProcesoClienteUpdate,
  TareaClienteListResponse,
  TareaClienteFilters,
  TareaClienteUpdate,
  PersonaResponsable,
} from '../../../types/proceso_cliente';

import {
  UNIDADES_REPETICION,
  VALORES_NEGOCIO_CLIENTE,
  COMPLEJIDADES_AUTOMATIZACION,
  PRIORIDADES_AUTOMATIZACION,
  ESTADOS_ANALISIS,
  VALOR_NEGOCIO_COLORS,
  PRIORIDAD_AUTOMATIZACION_COLORS,
  ESTADO_ANALISIS_COLORS,
} from '../../../types/proceso_cliente';

interface PanelDetalleProcesoClienteProps {
  proceso: ProcesoClienteDetalle | undefined;
  tareas: TareaClienteListResponse | undefined;
  personasDisponibles: PersonaResponsable[];
  isLoadingProceso: boolean;
  isLoadingTareas: boolean;
  tareaFilters: TareaClienteFilters;
  onClose: () => void;
  onUpdateProceso: (procesoId: string, data: ProcesoClienteUpdate) => Promise<void>;
  onUpdateTarea: (tareaId: string, data: TareaClienteUpdate) => Promise<void>;
  onTareaFiltersChange: (filters: TareaClienteFilters) => void;
}

export const PanelDetalleProcesoCliente: React.FC<PanelDetalleProcesoClienteProps> = ({
  proceso,
  tareas,
  personasDisponibles,
  isLoadingProceso,
  isLoadingTareas,
  tareaFilters,
  onClose,
  onUpdateProceso,
  onUpdateTarea,
  onTareaFiltersChange,
}) => {
  const [tareaSearchTerm, setTareaSearchTerm] = useState(tareaFilters.search || '');
  const [selectedTareaResponsables, setSelectedTareaResponsables] = useState<string[]>(
    tareaFilters.responsable_ids || []
  );

  const formatTiempo = (horas: number) => {
    if (horas === 0) return '0h';
    if (horas < 1) return `${Math.round(horas * 60)}min`;
    return `${horas.toFixed(1)}h`;
  };

  const getBadgeColor = (value: string | number | boolean | null, type: 'valor' | 'prioridad' | 'estado') => {
    if (!value || typeof value !== 'string') return 'bg-gray-100 text-gray-800';
    switch (type) {
      case 'valor':
        return VALOR_NEGOCIO_COLORS[value as keyof typeof VALOR_NEGOCIO_COLORS] || 'bg-gray-100 text-gray-800';
      case 'prioridad':
        return PRIORIDAD_AUTOMATIZACION_COLORS[value as keyof typeof PRIORIDAD_AUTOMATIZACION_COLORS] || 'bg-gray-100 text-gray-800';
      case 'estado':
        return ESTADO_ANALISIS_COLORS[value as keyof typeof ESTADO_ANALISIS_COLORS] || 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle task filter changes
  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      onTareaFiltersChange({
        search: tareaSearchTerm || undefined,
        responsable_ids: selectedTareaResponsables.length > 0 ? selectedTareaResponsables : undefined,
      });
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [tareaSearchTerm, selectedTareaResponsables, onTareaFiltersChange]);

  const handleProcesoFieldUpdate = async (field: string, value: string | number | boolean | null) => {
    if (!proceso) return;
    
    const updateData: ProcesoClienteUpdate = {
      [field]: value,
    };
    
    await onUpdateProceso(proceso.id, updateData);
  };

  const handleTareaFieldUpdate = async (tareaId: string, field: string, value: string | number | boolean | null) => {
    const updateData: TareaClienteUpdate = {
      [field]: value,
    };
    
    await onUpdateTarea(tareaId, updateData);
  };

  return (
    <div className="w-3/5 border-l border-gray-200 bg-white flex flex-col h-full">
      {/* Panel header - Fixed */}
      <div className="border-b border-gray-200 p-2 flex justify-between items-center flex-shrink-0 bg-gray-50">
        <h2 className="text-sm font-medium text-gray-900">
          Detalle del Proceso
        </h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors p-1 hover:bg-gray-200 rounded"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Panel content with independent scroll */}
      <div className={`flex-1 overflow-y-auto overflow-x-hidden p-3 min-h-0 ${styles.panelContent}`}>
        {isLoadingProceso ? (
          <div className="flex items-center justify-center h-32">
            <LoadingSpinner />
          </div>
        ) : proceso ? (
          <div className="space-y-4">
            {/* Process details */}
            <div>
              <EditableField
                label="Nombre del Proceso"
                value={proceso.nombre}
                type="text"
                onSave={(value) => handleProcesoFieldUpdate('nombre', value)}
                className="text-base font-medium text-gray-900 mb-3"
              />

              <EditableField
                label="Descripción"
                value={proceso.descripcion || ''}
                type="textarea"
                onSave={(value) => handleProcesoFieldUpdate('descripcion', value)}
                className="text-gray-600 mb-3 text-sm"
              />

              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <EditableField
                    label="Estado de Análisis"
                    value={proceso.estado_analisis || 'Pendiente'}
                    type="select"
                    options={ESTADOS_ANALISIS.map(estado => ({ value: estado, label: estado }))}
                    onSave={(value) => handleProcesoFieldUpdate('estado_analisis', value)}
                    renderValue={(value) => (
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getBadgeColor(value, 'estado')}`}>
                        {value}
                      </span>
                    )}
                  />
                </div>
                <div>
                  <EditableField
                    label="Valor de Negocio"
                    value={proceso.valor_negocio_cliente || ''}
                    type="select"
                    options={VALORES_NEGOCIO_CLIENTE.map(valor => ({ value: valor, label: valor }))}
                    onSave={(value) => handleProcesoFieldUpdate('valor_negocio_cliente', value)}
                    renderValue={(value) => value ? (
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getBadgeColor(value, 'valor')}`}>
                        {value}
                      </span>
                    ) : <span className="text-gray-500">Sin asignar</span>}
                  />
                </div>
                <div>
                  <EditableField
                    label="Prioridad Automatización"
                    value={proceso.prioridad_automatizacion_aceleralia || ''}
                    type="select"
                    options={PRIORIDADES_AUTOMATIZACION.map(prioridad => ({ value: prioridad, label: prioridad }))}
                    onSave={(value) => handleProcesoFieldUpdate('prioridad_automatizacion_aceleralia', value)}
                    renderValue={(value) => value ? (
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getBadgeColor(value, 'prioridad')}`}>
                        {value}
                      </span>
                    ) : <span className="text-gray-500">Sin asignar</span>}
                  />
                </div>
                <div>
                  <EditableField
                    label="Complejidad Automatización"
                    value={proceso.complejidad_automatizacion_aceleralia || ''}
                    type="select"
                    options={COMPLEJIDADES_AUTOMATIZACION.map(comp => ({ value: comp, label: comp }))}
                    onSave={(value) => handleProcesoFieldUpdate('complejidad_automatizacion_aceleralia', value)}
                    renderValue={(value) => value ? (
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                        {value}
                      </span>
                    ) : <span className="text-gray-500">Sin asignar</span>}
                  />
                </div>
                <div>
                  <EditableField
                    label="Duración (minutos)"
                    value={proceso.duracion_minutos_estimada?.toString() || ''}
                    type="number"
                    onSave={(value) => handleProcesoFieldUpdate('duracion_minutos_estimada', value ? parseInt(String(value)) || null : null)}
                  />
                </div>
                <div>
                  <EditableField
                    label="Unidad de Repetición"
                    value={proceso.unidad_repeticion_base || ''}
                    type="select"
                    options={UNIDADES_REPETICION.map(unidad => ({ value: unidad, label: unidad }))}
                    onSave={(value) => handleProcesoFieldUpdate('unidad_repeticion_base', value)}
                  />
                </div>
                <div>
                  <EditableField
                    label="Ocurrencias por Período"
                    value={proceso.numero_ocurrencias_frecuencia?.toString() || '1'}
                    type="number"
                    onSave={(value) => handleProcesoFieldUpdate('numero_ocurrencias_frecuencia', value ? parseInt(String(value)) || 1 : 1)}
                  />
                </div>
                <div>
                  <span className="font-medium text-gray-700">Tiempo estimado:</span>
                  <p className="text-gray-600">
                    {formatTiempo(proceso.tiempo_estimado_horas_mes)}/mes
                  </p>
                </div>
              </div>

              <div className="mt-3">
                <div className="flex items-center gap-3 flex-wrap">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={proceso.es_manual || false}
                      onChange={(e) => handleProcesoFieldUpdate('es_manual', e.target.checked)}
                      className="mr-1.5"
                    />
                    <span className="text-xs text-gray-700">Es manual</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={proceso.es_repetitivo || false}
                      onChange={(e) => handleProcesoFieldUpdate('es_repetitivo', e.target.checked)}
                      className="mr-1.5"
                    />
                    <span className="text-xs text-gray-700">Es repetitivo</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={proceso.es_cuello_botella || false}
                      onChange={(e) => handleProcesoFieldUpdate('es_cuello_botella', e.target.checked)}
                      className="mr-1.5"
                    />
                    <span className="text-xs text-gray-700">Es cuello de botella</span>
                  </label>
                </div>
              </div>

              <div className="mt-3">
                <EditableField
                  label="Información Adicional"
                  value={proceso.info_adicional || ''}
                  type="textarea"
                  onSave={(value) => handleProcesoFieldUpdate('info_adicional', value)}
                />
              </div>
            </div>

            {/* Tasks section */}
            <div>
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-sm font-medium text-gray-900">
                  Tareas ({tareas?.total || 0})
                </h4>
                {tareas && tareas.tiempo_total_horas_mes > 0 && (
                  <span className="text-xs text-gray-600">
                    {formatTiempo(tareas.tiempo_total_horas_mes)}/mes total
                  </span>
                )}
              </div>

              {/* Task filters */}
              <div className="grid grid-cols-1 gap-2 mb-3">
                <div>
                  <input
                    type="text"
                    value={tareaSearchTerm}
                    onChange={(e) => setTareaSearchTerm(e.target.value)}
                    placeholder="Buscar tareas..."
                    className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <select
                    multiple
                    value={selectedTareaResponsables}
                    onChange={(e) => setSelectedTareaResponsables(Array.from(e.target.selectedOptions, option => option.value))}
                    className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    size={2}
                  >
                    {personasDisponibles.map((persona) => (
                      <option key={persona.id} value={persona.id}>
                        {persona.nombre} {persona.apellidos}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Task list */}
              {isLoadingTareas ? (
                <div className="flex items-center justify-center h-32">
                  <LoadingSpinner />
                </div>
              ) : tareas?.tareas.length === 0 ? (
                <p className="text-gray-500 text-center py-3 text-xs">No se encontraron tareas</p>
              ) : (
                <div className="space-y-2">
                  {tareas?.tareas.map((tarea) => (
                    <div
                      key={tarea.id}
                      className="border border-gray-200 rounded-lg p-2 hover:bg-gray-50"
                    >
                      <EditableField
                        label="Nombre de la Tarea"
                        value={tarea.nombre_tarea_cliente}
                        type="text"
                        onSave={(value) => handleTareaFieldUpdate(tarea.id, 'nombre_tarea_cliente', value)}
                        className="font-medium text-gray-900 text-sm"
                      />

                      <EditableField
                        label="Descripción"
                        value={tarea.descripcion_tarea_cliente || ''}
                        type="textarea"
                        onSave={(value) => handleTareaFieldUpdate(tarea.id, 'descripcion_tarea_cliente', value)}
                        className="text-xs text-gray-600 mt-1"
                      />

                      <div className="grid grid-cols-2 gap-2 mt-2">
                        <EditableField
                          label="Duración (min)"
                          value={tarea.duracion_minutos_estimada?.toString() || ''}
                          type="number"
                          onSave={(value) => handleTareaFieldUpdate(tarea.id, 'duracion_minutos_estimada', value ? parseInt(String(value)) || null : null)}
                        />
                        <EditableField
                          label="Unidad"
                          value={tarea.unidad_repeticion_base || ''}
                          type="select"
                          options={UNIDADES_REPETICION.map(unidad => ({ value: unidad, label: unidad }))}
                          onSave={(value) => handleTareaFieldUpdate(tarea.id, 'unidad_repeticion_base', value)}
                        />
                      </div>

                      <div className="flex justify-between items-center mt-2">
                        <div className="text-xs text-gray-500 truncate flex-1 mr-2">
                          {tarea.responsables.length > 0 ? (
                            <span>
                              👤 {tarea.responsables.map(r => r.nombre).join(', ')}
                            </span>
                          ) : (
                            <span>Sin responsables</span>
                          )}
                        </div>
                        <div className="text-xs text-gray-500 flex-shrink-0">
                          ⏱️ {formatTiempo(tarea.tiempo_estimado_horas_mes)}/mes
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        ) : (
          <p className="text-gray-500 text-center py-8">
            Selecciona un proceso para ver sus detalles
          </p>
        )}
      </div>
    </div>
  );
};
